import 'dart:typed_data';

import 'package:flutter/material.dart';
import 'package:lindi/lindi.dart';
import 'package:lindi_sticker_widget/lindi_controller.dart';
import 'package:lindi_sticker_widget/lindi_sticker_icon.dart';
import 'package:lindi_sticker_widget/lindi_sticker_widget.dart';
import 'package:photo_editor2/helper/stickers.dart';
import 'package:photo_editor2/lindi/image_viewholder.dart';

class StickerScreen extends StatefulWidget {
  const StickerScreen({super.key});

  @override
  State<StickerScreen> createState() => _StickerScreenState();
}

class _StickerScreenState extends State<StickerScreen> {

  late ImageViewHolder imageViewHolder;
  late LindiController controller;

  int index = 0;

  @override
  void initState() {
    imageViewHolder = LindiInjector.get<ImageViewHolder>();
    controller = LindiController(
        icons: [
          LindiStickerIcon(
              icon: Icons.done,
              alignment: Alignment.topRight,
              onTap: () {
                controller.selectedWidget!.done();
              }),
          LindiStickerIcon(
              icon: Icons.lock_open,
              lockedIcon: Icons.lock,
              alignment: Alignment.topCenter,
              type: IconType.lock,
              onTap: () {
                controller.selectedWidget!.lock();
              }),
          LindiStickerIcon(
              icon: Icons.close,
              alignment: Alignment.topLeft,
              onTap: () {
                controller.selectedWidget!.delete();
              }),
          LindiStickerIcon(
              icon: Icons.layers,
              alignment: Alignment.bottomCenter,
              onTap: () {
                controller.selectedWidget!.stack();
              }),
          LindiStickerIcon(
              icon: Icons.flip,
              alignment: Alignment.bottomLeft,
              onTap: () {
                controller.selectedWidget!.flip();
              }),
          LindiStickerIcon(
              icon: Icons.crop_free,
              alignment: Alignment.bottomRight,
              type: IconType.resize
          ),
        ]
    );
    controller.onPositionChange((index) {
      debugPrint(
          "widgets size: ${controller.widgets.length}, current index: $index");
    });
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        backgroundColor:  const Color(0xFF2C2F36),
        leading: const CloseButton(),
        title: const Text('ستیکەر'),
        actions: [
          IconButton(
              onPressed: () async {
                Uint8List? image = await controller.saveAsUint8List();
                imageViewHolder.changeImage(image!);
                if(!mounted) return;
                Navigator.of(context).pop();
              },
              icon: const Icon(Icons.done)
          )
        ],
      ),
      body: Center(
        child: LindiBuilder(
          viewModel: imageViewHolder,
          builder: (BuildContext context) {
            if (imageViewHolder.currentImage != null) {
              return Container(
                color: Colors.red,
                child: LindiStickerWidget(
                  controller: controller,
                  child: Image.memory(imageViewHolder.currentImage!)
                ),
              );
            }
            return const Center(
              child: CircularProgressIndicator(),
            );
          },
        ),
      ),
      bottomNavigationBar: Container(
        width: double.infinity,
        height: 160,
        color:  const Color(0xFF2C2F36),
        child: SafeArea(
          child: Column(
            children: [
              Expanded(
                child: Container(
                  color:  const Color(0xFF2C2F36),
                  child: ListView.builder(
                    scrollDirection: Axis.horizontal,
                    itemCount: Stickers().list()[index].length,
                    itemBuilder: (BuildContext context, int idx){
                      String sticker = Stickers().list()[index][idx];
                      return Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 5),
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            SizedBox(
                              width: 60,
                              height: 60,
                              child: FittedBox(
                                fit: BoxFit.fill,
                                child: InkWell(
                                  onTap: (){
                                    controller.add(
                                        Image.asset(sticker, width: 100)
                                    );
                                  },
                                  child: Image.asset(sticker),
                                ),
                              ),
                            ),
                          ],
                        ),
                      );
                    },
                  )
                ),
              ),
              SingleChildScrollView(
                child: Row(
                  children: [
                    for(int i = 0; i < Stickers().list().length; i++)
                    _bottomBatItem(
                      i,
                      Stickers().list()[i][0],
                      onPress: () {
                        setState(() {
                          index = i;
                        });
                      }
                    )
                  ],
                ),
              )
            ],
          ),
        ),
      ),
    );
  }

  Widget _bottomBatItem(int idx, String icon, {required onPress}){
    return InkWell(
      onTap: onPress,
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 10),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 8),
              child: Container(
                color: index == idx ? Colors.blue : Colors.transparent,
                height: 2,
                width: 20,
              ),
            ),
            Padding(
              padding: const EdgeInsets.all(8.0),
              child: Image.asset(icon, width: 30),
            ),
          ],
        ),
      ),
    );
  }

}
