import 'dart:io';
import 'dart:typed_data';
import 'package:file_picker/file_picker.dart';
import 'package:video_thumbnail/video_thumbnail.dart';
import 'package:path_provider/path_provider.dart';

class VideoFrameExtractor {
  
  /// Pick a video file from device storage
  static Future<File?> pickVideo() async {
    try {
      FilePickerResult? result = await FilePicker.platform.pickFiles(
        type: FileType.video,
        allowMultiple: false,
      );

      if (result != null && result.files.single.path != null) {
        return File(result.files.single.path!);
      }
      return null;
    } catch (e) {
      print('Error picking video: $e');
      return null;
    }
  }

  /// Extract a frame from video at specific time position
  static Future<File?> extractFrame({
    required File videoFile,
    int timeMs = 1000, // Default to 1 second
    int quality = 100,
  }) async {
    try {
      final String? thumbnailPath = await VideoThumbnail.thumbnailFile(
        video: videoFile.path,
        thumbnailPath: (await getTemporaryDirectory()).path,
        imageFormat: ImageFormat.JPEG,
        timeMs: timeMs,
        quality: quality,
      );

      if (thumbnailPath != null) {
        return File(thumbnailPath);
      }
      return null;
    } catch (e) {
      print('Error extracting frame: $e');
      return null;
    }
  }

  /// Extract frame as Uint8List for direct use
  static Future<Uint8List?> extractFrameAsBytes({
    required File videoFile,
    int timeMs = 1000,
    int quality = 100,
  }) async {
    try {
      final Uint8List? thumbnailData = await VideoThumbnail.thumbnailData(
        video: videoFile.path,
        imageFormat: ImageFormat.JPEG,
        timeMs: timeMs,
        quality: quality,
      );

      return thumbnailData;
    } catch (e) {
      print('Error extracting frame as bytes: $e');
      return null;
    }
  }

  /// Extract multiple frames from video at different time positions
  static Future<List<File>> extractMultipleFrames({
    required File videoFile,
    List<int> timePositions = const [1000, 3000, 5000], // Default positions
    int quality = 100,
  }) async {
    List<File> frames = [];
    
    for (int timeMs in timePositions) {
      try {
        final File? frame = await extractFrame(
          videoFile: videoFile,
          timeMs: timeMs,
          quality: quality,
        );
        
        if (frame != null) {
          frames.add(frame);
        }
      } catch (e) {
        print('Error extracting frame at ${timeMs}ms: $e');
      }
    }
    
    return frames;
  }

  /// Get video duration in milliseconds
  static Future<int?> getVideoDuration(File videoFile) async {
    try {
      // This is a simplified approach - you might want to use video_player
      // for more accurate duration detection
      return 10000; // Default 10 seconds - replace with actual implementation
    } catch (e) {
      print('Error getting video duration: $e');
      return null;
    }
  }
}
