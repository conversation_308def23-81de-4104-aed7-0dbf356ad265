name: photo_editor2
description: "A new Flutter project."
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.0+1

environment:
  sdk: ^3.7.2

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter
  cupertino_icons: ^1.0.8
  image_picker: ^1.1.2
  video_player: ^2.8.6
  video_thumbnail: ^0.5.3
  file_picker: ^8.0.0+1
  path_provider: ^2.1.3
  lindi: ^0.1.1
  screenshot: ^3.0.0
  colorfilter_generator: ^0.0.8
  blur: ^4.0.2
  pixel_color_picker: ^1.0.0
  flutter_colorpicker: ^1.1.0
  lindi_sticker_widget: ^1.0.3
  text_editor: ^0.7.0
  painter: ^2.0.0
  widget_mask: ^1.0.0
  matrix_gesture_detector: ^0.2.0-nullsafety.1
  image_gallery_saver: ^1.7.1
  get_it: ^7.6.0
  intl: ^0.20.2
  flutter_launcher_icons: ^0.11.0


  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
dependency_overrides:
  image_gallery_saver:
    git:
      url: https://github.com/knottx/image_gallery_saver.git
      ref: knottx-latest

dev_dependencies:
  flutter_test:
    sdk: flutter
  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^5.0.0

flutter_icons:
  android: true
  ios: true
  image_path: "assets/images/logo.jpg"

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:
  fonts:
    - family: Rudaw
      fonts:
        - asset: assets/fonts/rudawbold.ttf
    - family: NRT
      fonts:
        - asset: assets/fonts/NRT-Reg.ttf
    - family: NizarAldhabi
      fonts:
        - asset: assets/fonts/NizarAldhabi.ttf
    - family: Sirwan
      fonts:
        - asset: assets/fonts/SiRWAN_6.ttf
    - family: UniMahan
      fonts:
        - asset: assets/fonts/UniMahanNurhan.ttf
    - family: Lanyon
      fonts:
        - asset: assets/fonts/Lanyon-Regular.ttf
  uses-material-design: true
  generate: true
  assets:
     - assets/images/
     - assets/stickers/

# Flutter Localizations Configuration
flutter_localizations:
  arb-dir: lib/l10n
  template-arb-file: app_en.arb
  output-localization-file: app_localizations.dart



  # To add assets to your application, add an assets section, like this:
 
  #  - images/a_dot_burr.jpeg
   

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/to/asset-from-package

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/to/font-from-package
