import 'dart:io';
import 'dart:typed_data';
import 'package:flutter/material.dart';
import 'package:video_player/video_player.dart';
import 'package:lindi/lindi.dart';
import 'package:photo_editor2/helper/video_frame_extractor.dart';
import 'package:photo_editor2/lindi/image_viewholder.dart';

class VideoFramePickerScreen extends StatefulWidget {
  final File videoFile;

  const VideoFramePickerScreen({super.key, required this.videoFile});

  @override
  State<VideoFramePickerScreen> createState() => _VideoFramePickerScreenState();
}

class _VideoFramePickerScreenState extends State<VideoFramePickerScreen> {
  late VideoPlayerController _controller;
  late ImageViewHolder imageViewHolder;
  bool _isInitialized = false;
  bool _isExtracting = false;
  List<Uint8List> _extractedFrames = [];
  int _selectedFrameIndex = -1;

  @override
  void initState() {
    super.initState();
    imageViewHolder = LindiInjector.get<ImageViewHolder>();
    _initializeVideo();
    _extractSampleFrames();
  }

  void _initializeVideo() async {
    _controller = VideoPlayerController.file(widget.videoFile);
    await _controller.initialize();
    setState(() {
      _isInitialized = true;
    });
  }

  void _extractSampleFrames() async {
    setState(() {
      _isExtracting = true;
    });

    // Extract frames at different time positions
    final duration = _controller.value.duration.inMilliseconds;
    final positions = [
      (duration * 0.1).round(), // 10%
      (duration * 0.3).round(), // 30%
      (duration * 0.5).round(), // 50%
      (duration * 0.7).round(), // 70%
      (duration * 0.9).round(), // 90%
    ];

    List<Uint8List> frames = [];
    for (int timeMs in positions) {
      final frameData = await VideoFrameExtractor.extractFrameAsBytes(
        videoFile: widget.videoFile,
        timeMs: timeMs,
        quality: 80,
      );
      if (frameData != null) {
        frames.add(frameData);
      }
    }

    setState(() {
      _extractedFrames = frames;
      _isExtracting = false;
    });
  }

  void _extractCurrentFrame() async {
    if (!_isInitialized) return;

    setState(() {
      _isExtracting = true;
    });

    final currentPosition = _controller.value.position.inMilliseconds;
    final frameData = await VideoFrameExtractor.extractFrameAsBytes(
      videoFile: widget.videoFile,
      timeMs: currentPosition,
      quality: 100,
    );

    if (frameData != null) {
      imageViewHolder.changeImage(frameData);
      Navigator.of(context).pushReplacementNamed('/home');
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Failed to extract frame')),
      );
    }

    setState(() {
      _isExtracting = false;
    });
  }

  void _selectFrame(int index) {
    setState(() {
      _selectedFrameIndex = index;
    });
  }

  void _useSelectedFrame() {
    if (_selectedFrameIndex >= 0 && _selectedFrameIndex < _extractedFrames.length) {
      imageViewHolder.changeImage(_extractedFrames[_selectedFrameIndex]);
      Navigator.of(context).pushReplacementNamed('/home');
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFF2C2F36),
      appBar: AppBar(
        title: const Text('Select Frame from Video', style: TextStyle(color: Colors.white)),
        backgroundColor: const Color(0xFF2C2F36),
        iconTheme: const IconThemeData(color: Colors.white),
        actions: [
          if (_selectedFrameIndex >= 0)
            IconButton(
              onPressed: _useSelectedFrame,
              icon: const Icon(Icons.check, color: Colors.green),
            ),
        ],
      ),
      body: Column(
        children: [
          // Video Player Section
          if (_isInitialized)
            Container(
              height: 200,
              margin: const EdgeInsets.all(16),
              child: AspectRatio(
                aspectRatio: _controller.value.aspectRatio,
                child: VideoPlayer(_controller),
              ),
            ),
          
          // Video Controls
          if (_isInitialized)
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: Row(
                children: [
                  IconButton(
                    onPressed: () {
                      setState(() {
                        _controller.value.isPlaying
                            ? _controller.pause()
                            : _controller.play();
                      });
                    },
                    icon: Icon(
                      _controller.value.isPlaying ? Icons.pause : Icons.play_arrow,
                      color: Colors.white,
                    ),
                  ),
                  Expanded(
                    child: VideoProgressIndicator(
                      _controller,
                      allowScrubbing: true,
                      colors: const VideoProgressColors(
                        playedColor: Colors.blue,
                        bufferedColor: Colors.grey,
                        backgroundColor: Colors.black,
                      ),
                    ),
                  ),
                  IconButton(
                    onPressed: _isExtracting ? null : _extractCurrentFrame,
                    icon: _isExtracting
                        ? const SizedBox(
                            width: 20,
                            height: 20,
                            child: CircularProgressIndicator(strokeWidth: 2),
                          )
                        : const Icon(Icons.camera_alt, color: Colors.white),
                  ),
                ],
              ),
            ),

          const SizedBox(height: 20),

          // Sample Frames Section
          const Padding(
            padding: EdgeInsets.symmetric(horizontal: 16),
            child: Text(
              'Or select from sample frames:',
              style: TextStyle(color: Colors.white, fontSize: 16),
            ),
          ),

          const SizedBox(height: 10),

          // Sample Frames Grid
          if (_isExtracting)
            const Center(
              child: CircularProgressIndicator(),
            )
          else
            Expanded(
              child: GridView.builder(
                padding: const EdgeInsets.all(16),
                gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                  crossAxisCount: 2,
                  crossAxisSpacing: 10,
                  mainAxisSpacing: 10,
                  childAspectRatio: 16 / 9,
                ),
                itemCount: _extractedFrames.length,
                itemBuilder: (context, index) {
                  return GestureDetector(
                    onTap: () => _selectFrame(index),
                    child: Container(
                      decoration: BoxDecoration(
                        border: Border.all(
                          color: _selectedFrameIndex == index
                              ? Colors.blue
                              : Colors.transparent,
                          width: 3,
                        ),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: ClipRRect(
                        borderRadius: BorderRadius.circular(8),
                        child: Image.memory(
                          _extractedFrames[index],
                          fit: BoxFit.cover,
                        ),
                      ),
                    ),
                  );
                },
              ),
            ),
        ],
      ),
    );
  }
}
