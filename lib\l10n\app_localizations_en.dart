// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for English (`en`).
class AppLocalizationsEn extends AppLocalizations {
  AppLocalizationsEn([String locale = 'en']) : super(locale);

  @override
  String get appTitle => 'Photo Editor';

  @override
  String get createProject => 'Create Project';

  @override
  String get camera => 'Camera';

  @override
  String get collage => 'Collage';

  @override
  String get recentEdited => 'Recent Edited';

  @override
  String get startButton => 'Start';

  @override
  String get cameraButton => 'Camera';

  @override
  String get galleryButton => 'Gallery';

  @override
  String get saveButton => 'Save';

  @override
  String get undoButton => 'Undo';

  @override
  String get redoButton => 'Redo';
}
