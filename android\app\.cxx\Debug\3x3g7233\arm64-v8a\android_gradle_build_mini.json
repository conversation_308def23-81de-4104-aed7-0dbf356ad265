{"buildFiles": ["C:\\Users\\<USER>\\Downloads\\flutter_windows_3.24.4-stable\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Users\\<USER>\\Desktop\\photo_editor2\\android\\app\\.cxx\\Debug\\3x3g7233\\arm64-v8a", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Users\\<USER>\\Desktop\\photo_editor2\\android\\app\\.cxx\\Debug\\3x3g7233\\arm64-v8a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}