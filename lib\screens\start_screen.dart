import 'dart:async';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import 'package:lindi/lindi.dart';
import 'package:photo_editor2/helper/app_image_picker.dart';
import 'package:photo_editor2/helper/video_frame_extractor.dart';
import 'package:photo_editor2/lindi/image_viewholder.dart';
import 'package:photo_editor2/screens/video_frame_picker_screen.dart';

class StartScreen extends StatefulWidget {
  const StartScreen({super.key});

  @override
  State<StartScreen> createState() => _StartScreenState();
}

class _StartScreenState extends State<StartScreen> {
  late ImageViewHolder imageViewHolder;
  int _currentImageIndex = 0;
  Timer? _timer;
  final List<String> _images = [
    'assets/images/image2.JPG',
    'assets/images/image3.JPG',
    'assets/images/image1.JPG',
    'assets/images/image4.JPG',
  ];

  int _iconIndex = 1;

  void _iconTap(int index) {
    setState(() {
      _iconIndex = index;
    });

    if (index == 0) {
      _openCameraAndReturnHome();
    } else if (index == 1) {
      // Home tapped
    } else if (index == 2) {
      showDialog(
        context: context,
        builder: (context) {
          return AlertDialog(
            title: Text('About'),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(Icons.photo, size: 50, color: Colors.cyanAccent),
                SizedBox(height: 10),
                Text('Photo Editor App\nVersion 1.0.0'),
                SizedBox(height: 10),
                Text(
                  'This is a simple photo editing app with filters and collage features.',
                ),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: Text('Close'),
              ),
            ],
          );
        },
      ).then((_) {
        // After dialog is closed, switch back to Home (index 1)
        setState(() {
          _iconIndex = 1;
        });
      });
    }
  }

  @override
  void initState() {
    imageViewHolder = LindiInjector.get<ImageViewHolder>();
    super.initState();
    _startImageSwitching();
  }

  void _pickFromGallery() {
    AppImagePicker(source: ImageSource.gallery).pick(
      onPick: (File? image) {
        if (image != null) {
          imageViewHolder.changeImageFile(image);
          Navigator.of(context).pushReplacementNamed('/home');
        }
      },
    );
  }

  Future<void> _pickFromCamera() async {
    AppImagePicker(source: ImageSource.camera).pick(
      onPick: (File? image) {
        if (image != null) {
          imageViewHolder.changeImageFile(image);
          Navigator.of(context).pushReplacementNamed('/home');
        }
      },
    );
    setState(() {
      _iconIndex = 1;
    });
  }

  void _openCameraAndReturnHome() async {
    await _pickFromCamera(); // Wait until camera action is done

    // After coming back from camera, switch back to Home
    setState(() {
      _iconIndex = 1;
    });
  }

  Future<void> _pickFromVideo() async {
    final videoFile = await VideoFrameExtractor.pickVideo();
    if (videoFile != null && mounted) {
      Navigator.of(context).push(
        MaterialPageRoute(
          builder: (context) => VideoFramePickerScreen(videoFile: videoFile),
        ),
      );
    }
    if (mounted) {
      setState(() {
        _iconIndex = 1;
      });
    }
  }

  void _startImageSwitching() {
    _timer = Timer.periodic(Duration(seconds: 10), (Timer timer) {
      setState(() {
        _currentImageIndex = (_currentImageIndex + 1) % _images.length;
      });
    });
  }

  @override
  void dispose() {
    _timer?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFF1C1F26),
      body: SafeArea(
        child: Stack(
          alignment: Alignment.topCenter, // <<< This is important
          children: [
            Column(
              children: [
                const SizedBox(height: 20),

                // Buttons centered
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  child: Column(
                    children: [
                      // Gallery Button
                      ElevatedButton(
                        style: ElevatedButton.styleFrom(
                          backgroundColor: const Color(0xFF2C2F36),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(16),
                          ),
                          padding: const EdgeInsets.symmetric(
                            vertical: 20,
                            horizontal: 40,
                          ),
                        ),
                        onPressed: _pickFromGallery,
                        child: Column(
                          children: [
                            Row(
                              mainAxisSize: MainAxisSize.min,
                              children: const [
                                Text(
                                  'زیادکردنی',
                                  style: TextStyle(
                                    fontSize: 20,
                                    color: Colors.white,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                                SizedBox(width: 8),
                                Icon(
                                  Icons.photo_library,
                                  color: Colors.cyanAccent,
                                ),
                              ],
                            ),
                            SizedBox(height: 8),
                            Text(
                              'وێنەیەکی نوێ',
                              style: TextStyle(
                                fontSize: 22,
                                color: Colors.white,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ],
                        ),
                      ),

                      const SizedBox(height: 16),

                      // Video Button
                      ElevatedButton(
                        style: ElevatedButton.styleFrom(
                          backgroundColor: const Color(0xFF2C2F36),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(16),
                          ),
                          padding: const EdgeInsets.symmetric(
                            vertical: 20,
                            horizontal: 40,
                          ),
                        ),
                        onPressed: _pickFromVideo,
                        child: Column(
                          children: [
                            Row(
                              mainAxisSize: MainAxisSize.min,
                              children: const [
                                Text(
                                  'هەڵبژاردنی',
                                  style: TextStyle(
                                    fontSize: 20,
                                    color: Colors.white,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                                SizedBox(width: 8),
                                Icon(Icons.video_library, color: Colors.orange),
                              ],
                            ),
                            SizedBox(height: 8),
                            Text(
                              'فرەیم لە ڤیدیۆ',
                              style: TextStyle(
                                fontSize: 22,
                                color: Colors.white,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),

            // ⭐⭐ Positioned slideshow correctly
            Positioned(
              bottom: 5, // control vertical distance
              left: 3,
              right: 3,
              child: ClipRRect(
                borderRadius: BorderRadius.circular(30),
                child: Container(
                  color: Colors.grey.shade800,
                  height: 450,
                  child: Image.asset(
                    _images[_currentImageIndex],
                    width: double.infinity,
                    height: 450,
                    fit: BoxFit.cover, // Keep cover to fill the area nicely
                  ),
                ),
              ),
            ),
          ],
        ),
      ),

      bottomNavigationBar: BottomNavigationBar(
        backgroundColor: const Color(0xFF1C1F26),
        selectedItemColor: Colors.cyanAccent,
        unselectedItemColor: Colors.white60,
        currentIndex: _iconIndex, // This highlights the selected tab
        onTap: _iconTap,
        items: const [
          BottomNavigationBarItem(
            icon: Icon(Icons.camera_alt),
            label: 'کامێرا',
          ),
          BottomNavigationBarItem(icon: Icon(Icons.home), label: 'ماڵەوە'),
          BottomNavigationBarItem(icon: Icon(Icons.info), label: 'دەربارە'),
        ],
      ),
    );
  }
}
