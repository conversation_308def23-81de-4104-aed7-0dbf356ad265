{"buildFiles": ["C:\\Users\\<USER>\\Downloads\\flutter_windows_3.24.4-stable\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Users\\<USER>\\Desktop\\photo_editor2\\android\\app\\.cxx\\RelWithDebInfo\\2d1i5c3b\\x86", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Users\\<USER>\\Desktop\\photo_editor2\\android\\app\\.cxx\\RelWithDebInfo\\2d1i5c3b\\x86", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}