import 'package:flutter/material.dart';
import 'package:image_gallery_saver/image_gallery_saver.dart';
import 'package:lindi/lindi.dart';
import 'package:photo_editor2/lindi/image_viewholder.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {

  late ImageViewHolder imageViewHolder;

  @override
  void initState() {
    imageViewHolder = LindiInjector.get<ImageViewHolder>();
    super.initState();
  }

  _savePhoto() async {
    final result = await ImageGallerySaver.saveImage(
        imageViewHolder.currentImage!,
        quality: 100,
        name: "${DateTime.now().millisecondsSinceEpoch}");
    if(!mounted) return false;
    if(result['isSuccess']){
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('وێنەکە بارکرا بۆ گەلەری'),
        )
      );
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('شتێک بە غەڵەت ڕویدا'),
          )
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        backgroundColor:  const Color(0xFF2C2F36),
        title: const Text("دەستکاریکەری وێنە"),
        leading: CloseButton(
          onPressed: () {
            Navigator.of(context).pushReplacementNamed('/');
          },
        ),
        actions: [
          TextButton(
            onPressed: (){
              _savePhoto();
            },
            child: const Text('بارکردن')
          )
        ],
      ),
      body: Stack(
        children: [
          Center(
            child: LindiBuilder(
              viewModel: imageViewHolder,
              builder: (BuildContext context){
                if(imageViewHolder.currentImage != null){
                  return Image.memory(
                    imageViewHolder.currentImage!,
                  );
                }
                return const Center(
                  child: CircularProgressIndicator(),
                );
              },
            ),
          ),
          Align(
            alignment: Alignment.topCenter,
            child: Container(
              margin: const EdgeInsets.all(15),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(20),
                color:  const Color(0xFF2C2F36)
              ),
              child: LindiBuilder(
                viewModel: imageViewHolder,
                builder: (context) {
                  return Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      IconButton(
                        onPressed: () {
                          imageViewHolder.undo();
                        },
                        icon: Icon(Icons.undo,
                            color: imageViewHolder.canUndo ? Colors.white : Colors.white10
                        ),
                      ),
                      IconButton(
                        onPressed: () {
                          imageViewHolder.redo();
                        },
                        icon: Icon(Icons.redo,
                            color: imageViewHolder.canRedo ? Colors.white : Colors.white10
                        ),
                      ),
                    ],
                  );
                }
              ),
            ),
          )
        ],
      ),
      bottomNavigationBar: Container(
        width: double.infinity,
        height: 100,
        color:  const Color(0xFF2C2F36),
        child: SafeArea(
          child: SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            child: Row(
              children: [
                _bottomBatItem(Icons.crop_rotate, 'قرتاندن',
                  onPress: () {
                    Navigator.of(context).pushNamed('/crop');
                  }
                ),
                _bottomBatItem(Icons.filter_vintage_outlined, 'فلتەرەکان',
                  onPress: () {
                    Navigator.of(context).pushNamed('/filter');
                  }
                ),
                _bottomBatItem(Icons.tune, 'ڕێکخستن',
                    onPress: () {
                      Navigator.of(context).pushNamed('/adjust');
                    }
                ),
                _bottomBatItem(Icons.fit_screen_sharp, 'گونجاندن',
                    onPress: () {
                      Navigator.of(context).pushNamed('/fit');
                    }
                ),
                _bottomBatItem(Icons.border_color_outlined, 'تینت',
                    onPress: () {
                      Navigator.of(context).pushNamed('/tint');
                    }
                ),
                _bottomBatItem(Icons.blur_circular, 'تەڵخی',
                    onPress: () {
                      Navigator.of(context).pushNamed('/blur');
                    }
                ),
                _bottomBatItem(Icons.emoji_emotions_outlined, 'ستیکەر',
                    onPress: () {
                      Navigator.of(context).pushNamed('/sticker');
                    }
                ),
                _bottomBatItem(Icons.text_fields, 'نوسین',
                    onPress: () {
                      Navigator.of(context).pushNamed('/text');
                    }
                ),
                _bottomBatItem(Icons.draw, 'وێنەکێشان',
                    onPress: () {
                      Navigator.of(context).pushNamed('/draw');
                    }
                ),
                _bottomBatItem(Icons.star_border, 'ماسک',
                    onPress: () {
                      Navigator.of(context).pushNamed('/mask');
                    }
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _bottomBatItem(IconData icon, String title, {required onPress}){
    return InkWell(
      onTap: onPress,
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 15),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(icon, color: Colors.white),
            const SizedBox(height: 3),
            Text(title,
              style: const TextStyle(
                color: Colors.white70
              ),
            )
          ],
        ),
      ),
    );
  }

}
