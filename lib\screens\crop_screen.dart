import 'dart:typed_data';
import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:lindi/lindi.dart';
import 'package:photo_editor2/cropImage/src/crop_controller.dart';
import 'package:photo_editor2/cropImage/src/crop_image.dart';
import 'package:photo_editor2/lindi/image_viewholder.dart';
import 'dart:ui' as ui;

class CropScreen extends StatefulWidget {
  const CropScreen({super.key});

  @override
  State<CropScreen> createState() => _CropScreenState();
}

class _CropScreenState extends State<CropScreen> {

  final controller = CropController(
    aspectRatio: 1,
    defaultCrop: const Rect.fromLTRB(0.1, 0.1, 0.9, 0.9),
  );
  late ImageViewHolder imageViewHolder;

  @override
  void initState() {
    imageViewHolder = LindiInjector.get<ImageViewHolder>();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        backgroundColor:  const Color(0xFF2C2F36),
        leading: const CloseButton(),
        title: const Text('قرتاندن'),
        actions: [
          IconButton(
              onPressed: () async {
                ui.Image bitmap = await controller.croppedBitmap();
                ByteData? data = await bitmap.toByteData(format: ImageByteFormat.png);
                Uint8List bytes = data!.buffer.asUint8List();
                imageViewHolder.changeImage(bytes);
                if(!mounted) return;
                Navigator.of(context).pop();
              },
              icon: const Icon(Icons.done)
          )
        ],
      ),
      body: Center(
        child: LindiBuilder(
          viewModel: imageViewHolder,
          builder: (BuildContext context) {
            if (imageViewHolder.currentImage != null) {
              return CropImage(
                controller: controller,
                image: Image.memory(imageViewHolder.currentImage!),
              );
            }
            return const Center(
              child: CircularProgressIndicator(),
            );
          },
        ),
      ),
      bottomNavigationBar: Container(
        width: double.infinity,
        height: 100,
        color:  const Color(0xFF2C2F36),
        child: SafeArea(
          child: SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            child: Row(
              children: [
                _bottomBatItem(
                    child: const Icon(Icons.rotate_90_degrees_ccw_outlined,
                        color: Colors.white),
                    onPress: () {
                      controller.rotateLeft();
                    }
                ),
                _bottomBatItem(
                    child: const Icon(Icons.rotate_90_degrees_cw_outlined,
                        color: Colors.white),
                    onPress: () {
                      controller.rotateRight();
                    }
                ),
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 5),
                  child: Container(
                    color: Colors.white70,
                    height: 20,
                    width: 1,
                  ),
                ),
                _bottomBatItem(
                    child: const Text('ئازاد',
                      style: TextStyle(
                        color: Colors.white
                      ),
                    ),
                    onPress: () {
                      controller.aspectRatio = null;
                      controller.crop = const Rect.fromLTRB(0.1, 0.1, 0.9, 0.9);
                    }
                ),
                _bottomBatItem(
                    child: const Text('١:١',
                      style: TextStyle(
                          color: Colors.white
                      ),
                    ),
                    onPress: () {
                      controller.aspectRatio = 1;
                      controller.crop = const Rect.fromLTRB(0.1, 0.1, 0.9, 0.9);
                    }
                ),
                _bottomBatItem(
                    child: const Text('٢:١',
                      style: TextStyle(
                          color: Colors.white
                      ),
                    ),
                    onPress: () {
                      controller.aspectRatio = 2;
                      controller.crop = const Rect.fromLTRB(0.1, 0.1, 0.9, 0.9);
                    }
                ),
                _bottomBatItem(
                    child: const Text('١:٢',
                      style: TextStyle(
                          color: Colors.white
                      ),
                    ),
                    onPress: () {
                      controller.aspectRatio = 1 / 2;
                      controller.crop = const Rect.fromLTRB(0.1, 0.1, 0.9, 0.9);
                    }
                ),
                _bottomBatItem(
                    child: const Text('٤:٣',
                      style: TextStyle(
                          color: Colors.white
                      ),
                    ),
                    onPress: () {
                      controller.aspectRatio = 4 / 3;
                      controller.crop = const Rect.fromLTRB(0.1, 0.1, 0.9, 0.9);
                    }
                ),
                _bottomBatItem(
                    child: const Text('٣:٤',
                      style: TextStyle(
                          color: Colors.white
                      ),
                    ),
                    onPress: () {
                      controller.aspectRatio = 3 / 4;
                      controller.crop = const Rect.fromLTRB(0.1, 0.1, 0.9, 0.9);
                    }
                ),
                _bottomBatItem(
                    child: const Text('١٦:٩',
                      style: TextStyle(
                          color: Colors.white
                      ),
                    ),
                    onPress: () {
                      controller.aspectRatio = 16 / 9;
                      controller.crop = const Rect.fromLTRB(0.1, 0.1, 0.9, 0.9);
                    }
                ),
                _bottomBatItem(
                    child: const Text('٩:١٦',
                      style: TextStyle(
                          color: Colors.white
                      ),
                    ),
                    onPress: () {
                      controller.aspectRatio = 9 / 16;
                      controller.crop = const Rect.fromLTRB(0.1, 0.1, 0.9, 0.9);
                    }
                )
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _bottomBatItem({required child, required onPress}) {
    return InkWell(
      onTap: onPress,
      child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 15),
          child: Center(
            child: child,
          )
      ),
    );
  }
}
