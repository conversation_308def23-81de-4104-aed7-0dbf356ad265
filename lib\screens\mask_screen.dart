import 'dart:typed_data';

import 'package:flutter/material.dart';
import 'package:lindi/lindi.dart';
import 'package:photo_editor2/helper/shapes.dart';
import 'package:photo_editor2/lindi/image_viewholder.dart';
import 'package:photo_editor2/widgets/gesture_detector_widget.dart';
import 'package:screenshot/screenshot.dart';
import 'package:widget_mask/widget_mask.dart';

class MaskScreen extends StatefulWidget {
  const MaskScreen({super.key});

  @override
  State<MaskScreen> createState() => _MaskScreenState();
}

class _MaskScreenState extends State<MaskScreen> {

  late ImageViewHolder imageViewHolder;
  Uint8List? currentImage;
  ScreenshotController screenshotController = ScreenshotController();

  BlendMode blendMode = BlendMode.dstIn;
  IconData iconData = Shapes().list()[0];
  double opacity = 1;

  @override
  void initState() {
    imageViewHolder = LindiInjector.get<ImageViewHolder>();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        backgroundColor:  const Color(0xFF2C2F36),
        leading: const CloseButton(),
        title: const Text('ماسک'),
        actions: [
          IconButton(
              onPressed: () async {
                Uint8List? bytes = await screenshotController.capture();
                imageViewHolder.changeImage(bytes!);
                if(!mounted) return;
                Navigator.of(context).pop();
              },
              icon: const Icon(Icons.done)
          )
        ],
      ),
      body: Center(
        child: LindiBuilder(
          viewModel: imageViewHolder,
          builder: (BuildContext context) {
            if (imageViewHolder.currentImage != null) {
              currentImage = imageViewHolder.currentImage;
              return Screenshot(
                  controller: screenshotController,
                  child: WidgetMask(
                    childSaveLayer: true,
                    blendMode: blendMode,
                    mask: Center(
                      child: Stack(
                        children: [
                          Container(
                            color: Colors.white.withOpacity(0.4),
                          ),
                          GestureDetectorWidget(
                              child: Icon(iconData, color: Colors.white.withOpacity(opacity), size: 250)
                          )
                        ],
                      ),
                    ),
                    child: Image.memory(imageViewHolder.currentImage!),
                  )
              );
            }
            return const Center(
              child: CircularProgressIndicator(),
            );
          },
        ),
      ),
      bottomNavigationBar: Container(
        width: double.infinity,
        height: 140,
        color:  const Color(0xFF2C2F36),
        child: SafeArea(
          child: Column(
            children: [
              SingleChildScrollView(
                scrollDirection: Axis.horizontal,
                child: Row(
                  children: [
                    TextButton(
                      onPressed: (){
                        setState(() {
                          opacity = 1;
                          blendMode = BlendMode.dstIn;
                        });
                      },
                      child: const Text('Dstin',
                        style: TextStyle(
                          color: Colors.white
                        ),
                      )
                    ),
                    TextButton(
                        onPressed: (){
                          setState(() {
                            blendMode = BlendMode.overlay;
                          });
                        },
                        child: const Text('Overlay',
                            style: TextStyle(
                                color: Colors.white
                            )
                        )
                    ),
                    TextButton(
                        onPressed: (){
                          setState(() {
                            opacity = 0.7;
                            blendMode = BlendMode.screen;
                          });
                        },
                        child: const Text('شاشە',
                            style: TextStyle(
                                color: Colors.white
                            )
                        )
                    ),
                    TextButton(
                        onPressed: (){
                          setState(() {
                            blendMode = BlendMode.saturation;
                          });
                        },
                        child: const Text('تێری',
                            style: TextStyle(
                                color: Colors.white
                            )
                        )
                    ),
                    TextButton(
                        onPressed: (){
                          setState(() {
                            blendMode = BlendMode.difference;
                          });
                        },
                        child: const Text('جیاوازی',
                            style: TextStyle(
                                color: Colors.white
                            )
                        )
                    )
                  ],
                ),
              ),
              Expanded(
                child: SingleChildScrollView(
                  scrollDirection: Axis.horizontal,
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      for(int i = 0; i < Shapes().list().length; i++)
                        _bottomBatItem(
                            Shapes().list()[i],
                            onPress: () {
                              setState(() {
                                iconData = Shapes().list()[i];
                              });
                            }
                        )
                    ],
                  ),
                ),
              )
            ],
          ),
        ),
      ),
    );
  }

  Widget _bottomBatItem(IconData icon, {required onPress}){
    return InkWell(
      onTap: onPress,
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 10),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Padding(
              padding: const EdgeInsets.all(8.0),
              child: Icon(icon, size: 40, color: Colors.white),
            ),
          ],
        ),
      ),
    );
  }

}
