import 'package:flutter/material.dart';
import 'package:lindi/lindi.dart';
import 'package:photo_editor2/screens/blur_screen.dart';
import 'package:photo_editor2/screens/crop_screen.dart';
import 'package:photo_editor2/screens/draw_screen.dart';
import 'package:photo_editor2/screens/filter_screen.dart';
import 'package:photo_editor2/screens/fit_screen.dart';
import 'package:photo_editor2/screens/home_screen.dart';
import 'package:photo_editor2/screens/mask_screen.dart';
import 'package:photo_editor2/screens/start_screen.dart';
import 'package:photo_editor2/screens/sticker_screen.dart';
import 'package:photo_editor2/screens/text_screen.dart';
import 'package:photo_editor2/screens/tint_screen.dart';
import 'package:photo_editor2/lindi/image_viewholder.dart';
import 'package:photo_editor2/screens/adjust_screen.dart';

import 'package:photo_editor2/screens/splash_screen.dart';

void main() {
  LindiInjector.register(ImageViewHolder());
  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  // This widget is the root of your application.
  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Photo Editor',
      debugShowCheckedModeBanner: false,
      theme: ThemeData(
        fontFamily: 'Lanyon',
        scaffoldBackgroundColor: const Color(0xFF2C2F36),
        primarySwatch: Colors.blue,
        appBarTheme: const AppBarTheme(
          color: Color(0xFF2C2F36),
          centerTitle: true,
          elevation: 0,
          titleTextStyle: TextStyle(color: Colors.white, fontSize: 22),
        ),
        textButtonTheme: TextButtonThemeData(
          style: ButtonStyle(
            foregroundColor: WidgetStateProperty.all(Colors.blue),
          ),
        ),
        iconButtonTheme: IconButtonThemeData(
          style: ButtonStyle(
            foregroundColor: WidgetStateProperty.all(Colors.white),
          ),
        ),
        sliderTheme: const SliderThemeData(
          showValueIndicator: ShowValueIndicator.always,
        ),
      ),
      initialRoute: '/splash',

      routes: <String, WidgetBuilder>{
        '/splash': (context) => const SplashScreen(),
        '/': (_) => const StartScreen(),
        '/home': (_) => const HomeScreen(),
        '/crop': (_) => const CropScreen(),
        '/filter': (_) => const FilterScreen(),
        '/adjust': (_) => const AdjustScreen(),
        '/fit': (_) => const FitScreen(),
        '/tint': (_) => const TintScreen(),
        '/blur': (_) => const BlurScreen(),
        '/sticker': (_) => const StickerScreen(),
        '/text': (_) => const TextScreen(),
        '/draw': (_) => const DrawScreen(),
        '/mask': (_) => const MaskScreen(),
      },
    );
  }
}
