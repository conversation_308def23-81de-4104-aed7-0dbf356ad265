import 'dart:async';
import 'package:flutter/material.dart';
import 'package:photo_editor2/screens/start_screen.dart';

class SplashScreen extends StatefulWidget {
  const SplashScreen({super.key});

  @override
  State<SplashScreen> createState() => _SplashScreenState();
}

class _SplashScreenState extends State<SplashScreen> {
  @override
  void initState() {
    super.initState();

    // Delay for 2 seconds, then go to StartScreen
Timer(const Duration(seconds: 4), () {
  Navigator.of(context).pushReplacement(
    PageRouteBuilder(
      pageBuilder: (context, animation1, animation2) => const StartScreen(),
      transitionDuration: Duration.zero, // No animation duration
      reverseTransitionDuration: Duration.zero, // No reverse animation
    ),
  );
});

  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFF1C1F26), // Same as StartScreen bg
      body: Center(
        child: Image.asset(
          'assets/images/mainLogo.png', // Your logo
          width: 150, // Adjust logo size
          height: 150,
        ),
      ),
    );
  }
}
